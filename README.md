# Bulk SMS Message Sender

A modern web application for sending SMS messages to single or multiple mobile numbers worldwide using the Twilio API.

## Features

- 📱 **Single SMS**: Send messages to individual phone numbers
- 📋 **Bulk SMS**: Send messages to multiple phone numbers at once
- 📁 **CSV Upload**: Upload phone numbers from CSV files
- 🌍 International phone number support
- ✅ Real-time form validation
- 📊 Progress tracking for bulk sends
- 📈 Detailed results with success/failure status
- 🎨 Modern, responsive UI with Tailwind CSS
- ⚡ Fast development with Vite and React
- 🔒 Secure API with proper error handling
- ⏱️ Rate limiting to respect Twilio API limits

## Tech Stack

- **Frontend**: React, Vite, Tailwind CSS, React Hook Form, Axios
- **Backend**: Node.js, Express.js, Twilio API
- **Styling**: Tailwind CSS

## Prerequisites

Before running this application, you need:

1. **Node.js** (version 16 or higher)
2. **Twilio Account** - Sign up at [twilio.com](https://www.twilio.com/)
3. **Twilio Phone Number** - Purchase a phone number from your Twilio console

## Setup Instructions

### 1. <PERSON>lone and Install Dependencies

```bash
# Install frontend dependencies
npm install

# Install backend dependencies
cd server
npm install
cd ..
```

### 2. Configure Twilio

1. Sign up for a Twilio account at [twilio.com](https://www.twilio.com/)
2. Go to your [Twilio Console](https://console.twilio.com/)
3. Find your Account SID and Auth Token
4. Purchase a phone number from the Phone Numbers section

### 3. Environment Setup

1. Copy the environment template:
```bash
cp server/.env.example server/.env
```

2. Edit `server/.env` with your Twilio credentials:
```env
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here
PORT=3001
```

### 4. Run the Application

Open two terminal windows:

**Terminal 1 - Backend Server:**
```bash
cd server
npm run dev
```

**Terminal 2 - Frontend Development Server:**
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Usage

### Single SMS
1. Open the application in your browser
2. Select the "Single SMS" tab
3. Enter a phone number with country code (e.g., +**********)
4. Type your message (max 1600 characters)
5. Click "Send Message"
6. Wait for confirmation or error message

### Bulk SMS
1. Select the "Bulk SMS" tab
2. Choose your input method:
   - **Type/Paste Numbers**: Enter phone numbers directly (one per line)
   - **Upload CSV File**: Upload a CSV file with phone numbers in the first column
3. Enter your message (max 1600 characters)
4. Click "Send Bulk SMS"
5. Monitor the progress bar and wait for detailed results

### CSV File Format
Your CSV file should have phone numbers in the first column:
```csv
Phone Number
+**********
+**********
+44123456789
```
Or simply:
```csv
+**********
+**********
+44123456789
```

## Phone Number Format

- Include country code (e.g., +1 for US, +44 for UK)
- Examples:
  - US: +**********
  - UK: +************
  - India: +************

## API Endpoints

- `POST /api/send-sms` - Send single SMS message
- `POST /api/send-bulk-sms` - Send bulk SMS messages
- `GET /api/health` - Health check

### Bulk SMS Limits
- Maximum 100 phone numbers per batch
- 1-second delay between each message to respect Twilio rate limits
- Individual error handling for each phone number

## Error Handling

The application handles various error scenarios:
- Invalid phone number format
- Missing Twilio credentials
- Twilio API errors
- Network connectivity issues
- Message length validation

## Security Notes

- Never commit your `.env` file to version control
- Keep your Twilio credentials secure
- Consider implementing rate limiting for production use
- Add authentication for production deployment

## Troubleshooting

### Common Issues

1. **"Twilio credentials not configured"**
   - Check your `.env` file in the server directory
   - Ensure all Twilio variables are set correctly

2. **"Invalid phone number"**
   - Make sure to include the country code
   - Use the format: +[country code][phone number]

3. **"Permission denied to send SMS"**
   - Check if the destination number is verified in your Twilio trial account
   - Upgrade to a paid Twilio account for unrestricted sending

## License

MIT License

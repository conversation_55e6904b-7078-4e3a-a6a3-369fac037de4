import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import twilio from 'twilio'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Initialize Twilio client (only if credentials are available)
let twilioClient = null
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
  twilioClient = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  )
}

// Validate phone number format
const validatePhoneNumber = (phoneNumber) => {
  // Basic validation for international phone numbers
  const phoneRegex = /^\+?[1-9]\d{1,14}$/
  return phoneRegex.test(phoneNumber)
}

// SMS sending endpoint
app.post('/api/send-sms', async (req, res) => {
  try {
    const { phoneNumber, message } = req.body

    // Validate input
    if (!phoneNumber || !message) {
      return res.status(400).json({
        error: 'Phone number and message are required'
      })
    }

    if (!validatePhoneNumber(phoneNumber)) {
      return res.status(400).json({
        error: 'Invalid phone number format. Please include country code (e.g., +**********)'
      })
    }

    if (message.length > 1600) {
      return res.status(400).json({
        error: 'Message too long. Maximum 1600 characters allowed.'
      })
    }

    // Check if Twilio credentials are configured
    if (!twilioClient || !process.env.TWILIO_PHONE_NUMBER) {
      return res.status(500).json({
        error: 'Twilio credentials not configured. Please check your environment variables.'
      })
    }

    // Send SMS using Twilio
    const twilioMessage = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    })

    console.log(`SMS sent successfully. SID: ${twilioMessage.sid}`)

    res.json({
      success: true,
      messageSid: twilioMessage.sid,
      message: 'SMS sent successfully'
    })

  } catch (error) {
    console.error('Error sending SMS:', error)

    // Handle Twilio-specific errors
    if (error.code) {
      let errorMessage = 'Failed to send SMS'
      
      switch (error.code) {
        case 21211:
          errorMessage = 'Invalid phone number'
          break
        case 21408:
          errorMessage = 'Permission denied to send SMS to this number'
          break
        case 21610:
          errorMessage = 'Message blocked - contains prohibited content'
          break
        case 21614:
          errorMessage = 'Invalid phone number format'
          break
        default:
          errorMessage = error.message || 'Failed to send SMS'
      }

      return res.status(400).json({
        error: errorMessage,
        code: error.code
      })
    }

    res.status(500).json({
      error: 'Internal server error. Please try again later.'
    })
  }
})

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'SMS Sender API is running',
    timestamp: new Date().toISOString()
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
  console.log(`Health check: http://localhost:${PORT}/api/health`)
  
  // Check if Twilio credentials are configured
  if (!twilioClient || !process.env.TWILIO_PHONE_NUMBER) {
    console.warn('⚠️  Twilio credentials not found. Please configure your .env file.')
  } else {
    console.log('✅ Twilio credentials configured')
  }
})

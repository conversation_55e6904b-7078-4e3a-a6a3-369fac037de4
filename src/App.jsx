import { useState } from 'react'
import MessageForm from './components/MessageForm'
import BulkMessageForm from './components/BulkMessageForm'

function App() {
  const [activeTab, setActiveTab] = useState('single')

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Bulk SMS Sender
          </h1>
          <p className="text-gray-600">
            Send text messages to single or multiple mobile numbers
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-2xl mx-auto mb-6">
          <div className="flex bg-white rounded-lg shadow p-1">
            <button
              onClick={() => setActiveTab('single')}
              className={`flex-1 py-2 px-4 rounded font-medium ${
                activeTab === 'single'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Single SMS
            </button>
            <button
              onClick={() => setActiveTab('bulk')}
              className={`flex-1 py-2 px-4 rounded font-medium ${
                activeTab === 'bulk'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Bulk SMS
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-2xl mx-auto">
          {activeTab === 'single' ? <MessageForm /> : <BulkMessageForm />}
        </div>

        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Powered by Twilio API</p>
        </div>
      </div>
    </div>
  )
}

export default App

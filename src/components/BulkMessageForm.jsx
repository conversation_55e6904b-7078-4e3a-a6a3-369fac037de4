import { useState, useRef } from 'react'
import { useForm } from 'react-hook-form'
import axios from 'axios'

const BulkMessageForm = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState(null)
  const [progress, setProgress] = useState({ current: 0, total: 0 })
  const [inputMethod, setInputMethod] = useState('textarea') // 'textarea' or 'file'
  const fileInputRef = useRef(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm()

  const watchedPhoneNumbers = watch('phoneNumbers', '')

  // Parse phone numbers from textarea
  const parsePhoneNumbers = (text) => {
    return text
      .split(/[\n,;]/)
      .map(num => num.trim())
      .filter(num => num.length > 0)
  }

  // Handle CSV file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target.result
      const lines = text.split('\n')
      const phoneNumbers = lines
        .map(line => {
          // Handle CSV format - take first column or the whole line if no commas
          const columns = line.split(',')
          return columns[0].trim()
        })
        .filter(num => num.length > 0 && num !== 'phone' && num !== 'Phone Number')
      
      // Update the textarea with parsed numbers
      const phoneNumbersText = phoneNumbers.join('\n')
      document.getElementById('phoneNumbers').value = phoneNumbersText
    }
    reader.readAsText(file)
  }

  const onSubmit = async (data) => {
    const phoneNumbers = inputMethod === 'textarea' 
      ? parsePhoneNumbers(data.phoneNumbers)
      : parsePhoneNumbers(data.phoneNumbers || '')

    if (phoneNumbers.length === 0) {
      setResults({
        success: false,
        message: 'Please enter at least one phone number',
        details: []
      })
      return
    }

    setIsLoading(true)
    setResults(null)
    setProgress({ current: 0, total: phoneNumbers.length })

    try {
      const response = await axios.post('/api/send-bulk-sms', {
        phoneNumbers,
        message: data.message
      })

      setResults(response.data)
      reset()
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Error sending bulk messages:', error)
      setResults({
        success: false,
        message: error.response?.data?.error || 'Failed to send bulk messages. Please try again.',
        details: []
      })
    } finally {
      setIsLoading(false)
      setProgress({ current: 0, total: 0 })
    }
  }

  const phoneCount = parsePhoneNumbers(watchedPhoneNumbers).length

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Input Method Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            How would you like to add phone numbers?
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                value="textarea"
                checked={inputMethod === 'textarea'}
                onChange={(e) => setInputMethod(e.target.value)}
                className="mr-2"
              />
              Type/Paste Numbers
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="file"
                checked={inputMethod === 'file'}
                onChange={(e) => setInputMethod(e.target.value)}
                className="mr-2"
              />
              Upload CSV File
            </label>
          </div>
        </div>

        {/* File Upload */}
        {inputMethod === 'file' && (
          <div>
            <label htmlFor="csvFile" className="block text-sm font-medium text-gray-700 mb-1">
              Upload CSV File
            </label>
            <input
              type="file"
              id="csvFile"
              ref={fileInputRef}
              accept=".csv,.txt"
              onChange={handleFileUpload}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Upload a CSV file with phone numbers in the first column
            </p>
          </div>
        )}

        {/* Phone Numbers Input */}
        <div>
          <label htmlFor="phoneNumbers" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Numbers {phoneCount > 0 && <span className="text-blue-600">({phoneCount} numbers)</span>}
          </label>
          <textarea
            id="phoneNumbers"
            rows={8}
            {...register('phoneNumbers', {
              required: 'At least one phone number is required',
              validate: (value) => {
                const numbers = parsePhoneNumbers(value)
                if (numbers.length === 0) return 'At least one phone number is required'
                if (numbers.length > 100) return 'Maximum 100 phone numbers allowed per batch'
                
                // Validate each phone number format
                const phoneRegex = /^\+?[1-9]\d{1,14}$/
                const invalidNumbers = numbers.filter(num => !phoneRegex.test(num))
                if (invalidNumbers.length > 0) {
                  return `Invalid phone number format: ${invalidNumbers.slice(0, 3).join(', ')}${invalidNumbers.length > 3 ? '...' : ''}`
                }
                return true
              }
            })}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
              errors.phoneNumbers ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter phone numbers (one per line):&#10;+1234567890&#10;+1987654321&#10;+44123456789"
          />
          {errors.phoneNumbers && (
            <p className="mt-1 text-sm text-red-600">{errors.phoneNumbers.message}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Enter one phone number per line. Include country codes (e.g., +1, +44, +91)
          </p>
        </div>

        {/* Message Input */}
        <div>
          <label htmlFor="bulkMessage" className="block text-sm font-medium text-gray-700 mb-1">
            Message
          </label>
          <textarea
            id="bulkMessage"
            rows={4}
            {...register('message', {
              required: 'Message is required',
              maxLength: {
                value: 1600,
                message: 'Message must be less than 1600 characters'
              }
            })}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
              errors.message ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your message here..."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading || phoneCount === 0}
          className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
            isLoading || phoneCount === 0
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
          } text-white`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sending to {progress.current}/{progress.total} numbers...
            </div>
          ) : (
            `Send Bulk SMS ${phoneCount > 0 ? `(${phoneCount} numbers)` : ''}`
          )}
        </button>
      </form>

      {/* Progress Bar */}
      {isLoading && progress.total > 0 && (
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{progress.current}/{progress.total}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(progress.current / progress.total) * 100}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className="mt-6">
          <div className={`p-4 rounded-md ${
            results.success 
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            <h3 className="font-medium mb-2">
              {results.success ? '✅ Bulk SMS Results' : '❌ Bulk SMS Failed'}
            </h3>
            <p className="mb-3">{results.message}</p>
            
            {results.details && results.details.length > 0 && (
              <div className="mt-3">
                <h4 className="font-medium mb-2">Detailed Results:</h4>
                <div className="max-h-40 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-1">Phone Number</th>
                        <th className="text-left py-1">Status</th>
                        <th className="text-left py-1">Message</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.details.map((result, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-1">{result.phoneNumber}</td>
                          <td className="py-1">
                            <span className={`px-2 py-1 rounded text-xs ${
                              result.success 
                                ? 'bg-green-200 text-green-800' 
                                : 'bg-red-200 text-red-800'
                            }`}>
                              {result.success ? 'Success' : 'Failed'}
                            </span>
                          </td>
                          <td className="py-1 text-xs">{result.message}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Info */}
      <div className="mt-6 text-xs text-gray-500">
        <p><strong>Bulk SMS Notes:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Maximum 100 phone numbers per batch</li>
          <li>Messages are sent with a small delay to respect API limits</li>
          <li>Each number is processed individually - some may succeed while others fail</li>
          <li>Phone numbers should include country code (e.g., +1 for US, +44 for UK)</li>
        </ul>
      </div>
    </div>
  )
}

export default BulkMessageForm

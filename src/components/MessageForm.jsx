import { useState } from 'react'
import { useForm } from 'react-hook-form'
import axios from 'axios'

const MessageForm = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm()

  const onSubmit = async (data) => {
    setIsLoading(true)
    setMessage('')
    setMessageType('')

    try {
      const response = await axios.post('/api/send-sms', {
        phoneNumber: data.phoneNumber,
        message: data.message
      })

      setMessage('Message sent successfully!')
      setMessageType('success')
      reset()
    } catch (error) {
      console.error('Error sending message:', error)
      setMessage(
        error.response?.data?.error || 
        'Failed to send message. Please try again.'
      )
      setMessageType('error')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 transform hover:scale-[1.02] transition-all duration-300">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-4 shadow-lg">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Send Single SMS</h2>
        <p className="text-gray-600">Send a message to one phone number</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        <div className="group">
          <label htmlFor="phoneNumber" className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            Phone Number
          </label>
          <div className="relative">
            <input
              type="tel"
              id="phoneNumber"
              {...register('phoneNumber', {
                required: 'Phone number is required',
                pattern: {
                  value: /^\+?[1-9]\d{1,14}$/,
                  message: 'Please enter a valid phone number (e.g., +1234567890)'
                }
              })}
              className={`w-full px-4 py-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 text-lg font-medium ${
                errors.phoneNumber
                  ? 'border-red-400 bg-red-50 focus:border-red-500 focus:ring-4 focus:ring-red-100'
                  : 'border-gray-200 bg-gray-50 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 group-hover:border-gray-300'
              }`}
              placeholder="+****************"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-4">
              <div className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                errors.phoneNumber ? 'bg-red-400' : 'bg-gray-300 group-hover:bg-blue-400'
              }`}></div>
            </div>
          </div>
          {errors.phoneNumber && (
            <div className="mt-3 flex items-center text-red-600 animate-fade-in">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm font-medium">{errors.phoneNumber.message}</p>
            </div>
          )}
        </div>

        <div className="group">
          <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
            Message Content
          </label>
          <div className="relative">
            <textarea
              id="message"
              rows={5}
              {...register('message', {
                required: 'Message is required',
                maxLength: {
                  value: 1600,
                  message: 'Message must be less than 1600 characters'
                }
              })}
              className={`w-full px-4 py-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 text-lg resize-none ${
                errors.message
                  ? 'border-red-400 bg-red-50 focus:border-red-500 focus:ring-4 focus:ring-red-100'
                  : 'border-gray-200 bg-gray-50 focus:border-purple-500 focus:ring-4 focus:ring-purple-100 group-hover:border-gray-300'
              }`}
              placeholder="Type your message here... ✨"
            />
            <div className="absolute bottom-3 right-3 text-xs text-gray-400 bg-white/80 backdrop-blur-sm rounded-lg px-2 py-1">
              {watch('message')?.length || 0}/1600
            </div>
          </div>
          {errors.message && (
            <div className="mt-3 flex items-center text-red-600 animate-fade-in">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm font-medium">{errors.message.message}</p>
            </div>
          )}
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-5 px-6 rounded-2xl font-bold text-lg transition-all duration-300 transform ${
            isLoading
              ? 'bg-gray-400 cursor-not-allowed scale-95'
              : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-200 hover:scale-105 shadow-lg hover:shadow-xl'
          } text-white`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
              <span>Sending Message...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              <span>Send Message</span>
            </div>
          )}
        </button>
      </form>

      {message && (
        <div className={`mt-6 p-6 rounded-2xl border-2 animate-fade-in ${
          messageType === 'success'
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800'
            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-center">
            {messageType === 'success' ? (
              <svg className="w-6 h-6 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            ) : (
              <svg className="w-6 h-6 mr-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            )}
            <span className="font-semibold text-lg">{message}</span>
          </div>
        </div>
      )}

      <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-100">
        <div className="flex items-start">
          <svg className="w-6 h-6 text-blue-500 mr-3 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-sm text-gray-700">
            <p className="font-semibold mb-2">📋 Quick Tips:</p>
            <ul className="space-y-1 text-gray-600">
              <li>• Include country code (e.g., +1 for US, +44 for UK)</li>
              <li>• This demo requires Twilio account and API credentials</li>
              <li>• Messages are delivered instantly worldwide</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MessageForm
